namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Interface for providing code generation templates.
    /// This abstraction allows for flexible template management and easier testing.
    /// </summary>
    public interface ITemplateProvider
    {
        /// <summary>
        /// Gets the template for generating ClickHouse data columns code.
        /// </summary>
        string GetClickHouseDataColumnsTemplate();

        /// <summary>
        /// Gets the template for generating column default values code.
        /// </summary>
        string GetColumnDefaultValuesTemplate();

        /// <summary>
        /// Processes a template with the provided data.
        /// </summary>
        /// <param name="template">The template string</param>
        /// <param name="data">Data to substitute in the template</param>
        /// <returns>Processed template string</returns>
        string ProcessTemplate(string template, Dictionary<string, object> data);
    }

    /// <summary>
    /// Default implementation of ITemplateProvider using simple string replacement.
    /// </summary>
    public class DefaultTemplateProvider : ITemplateProvider
    {
        public string GetClickHouseDataColumnsTemplate()
        {
            return @"using System.Collections.Generic;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Auto-generated class containing ClickHouse data column mappings.
    /// Generated on: {GenerationDate}
    /// </summary>
    public static class ClickhouseDataColumns
    {
        /// <summary>
        /// Dictionary mapping lowercase column names to their proper case equivalents.
        /// </summary>
        public static readonly Dictionary<string, string> AllCHBiDataColumns = new Dictionary<string, string>
        {ColumnMappings};
    }
}";
        }

        public string GetColumnDefaultValuesTemplate()
        {
            return @"using System.Collections.Generic;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Auto-generated class containing column default values.
    /// Generated on: {GenerationDate}
    /// </summary>
    public static class ColumnDefaultValues
    {
        /// <summary>
        /// Dictionary mapping column names to their default values.
        /// </summary>
        public static readonly Dictionary<string, string> DefaultValues = new Dictionary<string, string>
        {DefaultValueMappings};
    }
}";
        }

        public string ProcessTemplate(string template, Dictionary<string, object> data)
        {
            if (string.IsNullOrEmpty(template))
                throw new ArgumentException("Template cannot be null or empty", nameof(template));

            if (data == null)
                throw new ArgumentNullException(nameof(data));

            var result = template;
            foreach (var kvp in data)
            {
                var placeholder = "{" + kvp.Key + "}";
                result = result.Replace(placeholder, kvp.Value?.ToString() ?? string.Empty);
            }

            return result;
        }
    }

    /// <summary>
    /// Helper class for building code generation data.
    /// </summary>
    public static class CodeGenerationHelper
    {
        /// <summary>
        /// Builds a dictionary entries string for code generation.
        /// </summary>
        /// <param name="dictionary">Dictionary to convert to code</param>
        /// <param name="indentation">Indentation to use for each line</param>
        /// <returns>Formatted dictionary entries string</returns>
        public static string BuildDictionaryEntries(Dictionary<string, string> dictionary, string indentation = "            ")
        {
            if (dictionary == null || dictionary.Count == 0)
                return string.Empty;

            var entries = dictionary.Select(kvp => 
                $"{indentation}{{ \"{EscapeString(kvp.Key)}\", \"{EscapeString(kvp.Value)}\" }}");

            return "{\r\n" + string.Join(",\r\n", entries) + "\r\n        }";
        }

        /// <summary>
        /// Escapes special characters in strings for code generation.
        /// </summary>
        /// <param name="input">String to escape</param>
        /// <returns>Escaped string</returns>
        public static string EscapeString(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;

            return input.Replace("\\", "\\\\")
                       .Replace("\"", "\\\"")
                       .Replace("\r", "\\r")
                       .Replace("\n", "\\n")
                       .Replace("\t", "\\t");
        }

        /// <summary>
        /// Builds a list entries string for code generation.
        /// </summary>
        /// <param name="list">List to convert to code</param>
        /// <param name="indentation">Indentation to use for each line</param>
        /// <returns>Formatted list entries string</returns>
        public static string BuildListEntries(IEnumerable<string> list, string indentation = "            ")
        {
            if (list == null)
                return string.Empty;

            var entries = list.Select(item => $"{indentation}\"{EscapeString(item)}\"");
            return "{\r\n" + string.Join(",\r\n", entries) + "\r\n        }";
        }

        /// <summary>
        /// Generates a standard file header comment.
        /// </summary>
        /// <param name="description">Description of the generated file</param>
        /// <returns>File header comment</returns>
        public static string GenerateFileHeader(string description)
        {
            return $@"//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by BiMetadataGenerator.
//     {description}
//     
//     Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}
//     
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

";
        }
    }
}
