﻿using ClickHouse.Client.ADO;
using Microsoft.Data.SqlClient;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    public class QueryUtility
    {
        private static string CIBoxIPAddress = @"************";
        private static string CIDBPassword = "cheetahAdmin432!";
        
        //PME local login is gone
        //public static SqlConnection GetAzureSqlConnection(string servername, string databaseName, string userName = "<EMAIL>")
        //{
        //    string connectionString = $"Data Source={servername};User ID={userName};Initial Catalog={databaseName};Authentication=ActiveDirectoryInteractive";
        //    return new SqlConnection(connectionString);
        //}

        public static SqlConnection GetCISqlConnection()
        {
            string connectionString = $"Data Source={CIBoxIPAddress};User ID=cloudtest;Initial Catalog=AdvertiserBI_P1;Password={CIDBPassword}";
            return new SqlConnection(connectionString);
        }

        public static ClickHouseConnection GetClickhouseConnection()
        {
            string connectionString = @$"Host={CIBoxIPAddress};Protocol=http;Port=9123;Username=default;Password=;Timeout=600";
            return new ClickHouseConnection(connectionString);
        }
    }
}
