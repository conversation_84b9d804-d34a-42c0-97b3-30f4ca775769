using System.Text.Json;

namespace Microsoft.AdCenter.Advertiser.BIDatamart.Clickhouse
{
    /// <summary>
    /// Configuration class that holds all the hard-coded values previously embedded in BiMetadataGenerator.
    /// This allows for external configuration and easier testing.
    /// </summary>
    public class BiMetadataConfiguration
    {
        #region Constants

        /// <summary>
        /// Regex pattern to replace "ID" with "Id", excluding "LCID" which is a special case.
        /// </summary>
        public string IdReplacementPattern { get; set; } = @"(?<!LC)ID";

        /// <summary>
        /// Special symbols that indicate a token should not be processed as a column name.
        /// </summary>
        public char[] SpecialSymbols { get; set; } = { '<', '>', '=', '\'' };

        #endregion

        #region Report Configuration

        /// <summary>
        /// List of reports that are not present in the metadata table and need special handling.
        /// </summary>
        public IReadOnlyList<string> ReportsNotInMetaTable { get; set; } = new List<string>
        {
            "prc_EstimateRows_Dim",
            "rpt_AccountActivity",
            "rpt_CampaignActivity",
            "rpt_OrderSummary",
            "prc_OrderQualityScore_ui",
            "prc_CampaignExperimentSummary_ui",
            "prc_CampaignQualityScore_ui",
            "prc_AgeGenderSummary_ui",
            "rpt_GetCampaignInfo",
            "prc_GetAdLandingPageUrlDimensions_ui",
            "prc_KeywordQualityScore_ui",
            "rpt_KeywordAuctionSummary",
            "prc_AdExtensionsOrderAssociation_ui",
            "prc_KeywordQualityScore_ui",
            "prc_AdExtensionsByOrderV2_ui",
            "prc_AssetSummary_ui",
            "prc_AssetCombinationSummary_ui",
            "prc_GetAssetTextByIds_ui",
            "prc_GetMeteredCallDetails",
            "prc_AssetAdGroupSnapshotSummary_ui",
            "prc_AudienceSummary_ui",
            "prc_AssetSnapShotSummary_ui",
            "prc_FeedItemAdExtensionSummary_ui",
            "prc_TargetSummaryV2_ui",
            "rpt_NegativeKeywordConflictsList",
            "rpt_TANegativeKeywordConflictsList",
            "rpt_NegativeKeywordConflictsListV2",
            "prc_PerformanceTargetSummary_ui",
            "rpt_GetAccountInfo",
            "prc_ProfileSummary_ui",
            "prc_LeadFormExtensionDetails_ui",
            "prc_GetProductTargetsCountsAccountCampaign_ui",
            "prc_GetProductOfferCountsAccountCampaign_ui",
            "prc_ProductGroupSummary_V1_ui",
            "prc_GetNegativeKeywordConflictDetails",
            "prc_GetNegativeKeywordConflictStatus",
            "prc_ProductGroupSummaryForAccountCampaignOrder_ui",
            "prc_GetAccountQualityScore",
            "prc_GetLastLoadCompleteTime",
            "prc_GetProcedureDetails",
            "prc_GetTaskStatus_ui",
            "prc_GetSearchPhraseSummary",
            "prc_GetSearchQuerySummary",
            "prc_GetPGCriteriaSelectionSummary_Perf_ui",
            "rpt_GetChangeHistorySummary",
            "rpt_GetEntityValuesByEntityIds",
            "rpt_UserRoleChangeHistory",
            "prc_GetChangeHistorySummaryByUser",
            "prc_GetChangeHistorySummary_ui",
            "prc_GetChangeHistorySummaryCount_ui",
            "prc_GetEntityValuesByEntityIds",
            "prc_GetUsersOfChanges_V3",
            "prc_UpsertUndoneChange",
            "prc_GetCampaignConvData",
            "prc_GetBidSuggestion",
            "prc_GetAutoTargetWebsiteCoverage",
            "prc_GetAudienceRecommendation",
            "prc_GetAssetTextByAccountIdAssetId_v2",
            "prc_CheckAccountFactDataExists",
            "rpt_SearchInsightSummary",
            "rpt_SearchVerticalCategoryClickShareReport",
            "rpt_SearchVerticalCategoryInsightsReport",
            "prc_PublicLatestCompleteDTimLoad",
            "prc_UpliftSummary_ui",
        }.AsReadOnly();

        #endregion

        #region Column Configuration

        /// <summary>
        /// SQL keywords and reserved words that should not be treated as column names.
        /// </summary>
        public IReadOnlySet<string> NonColumns { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "case", "when", "then", "null", "else", "end", "as", "is", "not"
        };

        /// <summary>
        /// Columns that are deprecated and should be excluded from processing.
        /// </summary>
        public IReadOnlySet<string> DeprecatedColumns { get; set; } = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            "MaxSearchCPC", "MaxContentCPC", "UserDim1Name", "UserDim2Name", "UserDim3Name", "UserDim4Name", "UserDim5Name"
        };

        #endregion

        #region Table Configuration

        /// <summary>
        /// Dimension table prefixes that should not have views created for them.
        /// </summary>
        public IReadOnlyList<string> DimTablePrefixNotCreateView { get; set; } = new List<string>
        {
            "v", "#", "<", "AllDaily", "UserDefined", "DailyBidSuggestion"
        }.AsReadOnly();

        /// <summary>
        /// Dimension table names that do not end with "Dim" suffix.
        /// </summary>
        public IReadOnlyList<string> DimTableNameNotEndWithDim { get; set; } = new List<string>
        {
            "vAdExtension"
        }.AsReadOnly();

        #endregion

        #region Factory Methods

        /// <summary>
        /// Creates a configuration instance from a JSON file.
        /// </summary>
        /// <param name="configFilePath">Path to the JSON configuration file</param>
        /// <returns>BiMetadataConfiguration instance</returns>
        public static BiMetadataConfiguration FromJsonFile(string configFilePath)
        {
            if (string.IsNullOrWhiteSpace(configFilePath))
                throw new ArgumentException("Configuration file path cannot be null or empty", nameof(configFilePath));

            if (!File.Exists(configFilePath))
                throw new FileNotFoundException($"Configuration file not found: {configFilePath}");

            var json = File.ReadAllText(configFilePath);
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                ReadCommentHandling = JsonCommentHandling.Skip
            };

            return JsonSerializer.Deserialize<BiMetadataConfiguration>(json, options) 
                ?? throw new InvalidOperationException("Failed to deserialize configuration from JSON");
        }

        /// <summary>
        /// Saves the current configuration to a JSON file.
        /// </summary>
        /// <param name="configFilePath">Path where to save the JSON configuration file</param>
        public void SaveToJsonFile(string configFilePath)
        {
            if (string.IsNullOrWhiteSpace(configFilePath))
                throw new ArgumentException("Configuration file path cannot be null or empty", nameof(configFilePath));

            var options = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            var json = JsonSerializer.Serialize(this, options);
            File.WriteAllText(configFilePath, json);
        }

        #endregion
    }
}
